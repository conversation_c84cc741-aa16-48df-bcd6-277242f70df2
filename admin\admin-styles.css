/* Simple Admin Panel Styles - Student Level */
body {
    background-color: #f5f5f5;
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 0;
}

/* Simple header navigation */
.admin-nav {
    background-color: #333;
    padding: 15px;
    text-align: center;
}

.admin-nav a {
    color: white;
    text-decoration: none;
    margin: 0 15px;
    padding: 8px 15px;
    border-radius: 3px;
}

.admin-nav a:hover {
    background-color: #555;
}

.admin-nav a.active {
    background-color: #007bff;
}

/* Container */
.container {
    max-width: 1200px;
    margin: 20px auto;
    padding: 0 20px;
}

/* Simple card style */
.card {
    background: white;
    border: 1px solid #ddd;
    border-radius: 5px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.card h3 {
    margin-top: 0;
    color: #333;
}

/* Stats boxes */
.stats-row {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    margin-bottom: 30px;
}

.stat-box {
    background: white;
    border: 2px solid #ddd;
    border-radius: 5px;
    padding: 20px;
    text-align: center;
    flex: 1;
    min-width: 200px;
}

.stat-number {
    font-size: 2em;
    font-weight: bold;
    color: #007bff;
}

.stat-label {
    color: #666;
    margin-top: 5px;
}

/* Simple table */
.simple-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
}

.simple-table th,
.simple-table td {
    border: 1px solid #ddd;
    padding: 10px;
    text-align: left;
}

.simple-table th {
    background-color: #f8f9fa;
    font-weight: bold;
}

.simple-table tr:nth-child(even) {
    background-color: #f9f9f9;
}

/* Simple buttons */
.btn {
    padding: 8px 15px;
    border: none;
    border-radius: 3px;
    cursor: pointer;
    text-decoration: none;
    display: inline-block;
    margin: 2px;
}

.btn-primary {
    background-color: #007bff;
    color: white;
}

.btn-danger {
    background-color: #dc3545;
    color: white;
}

.btn-success {
    background-color: #28a745;
    color: white;
}

.btn-warning {
    background-color: #ffc107;
    color: black;
}

.btn:hover {
    opacity: 0.8;
}

/* Simple status badges */
.badge {
    padding: 3px 8px;
    border-radius: 3px;
    font-size: 12px;
    font-weight: bold;
}

.badge-success {
    background-color: #28a745;
    color: white;
}

.badge-danger {
    background-color: #dc3545;
    color: white;
}

.badge-secondary {
    background-color: #6c757d;
    color: white;
}

/* Page title */
h1 {
    color: #333;
    text-align: center;
    margin-bottom: 30px;
}

/* Form styling */
.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
}

.form-control {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 3px;
    box-sizing: border-box;
}

/* Responsive */
@media (max-width: 768px) {
    .stats-row {
        flex-direction: column;
    }
    
    .admin-nav a {
        display: block;
        margin: 5px 0;
    }
    
    .container {
        padding: 0 10px;
    }
}
