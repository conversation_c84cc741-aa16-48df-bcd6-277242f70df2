<?php
// Database connection test file
// This file will help diagnose the database connection issue

echo "<h2>Database Connection Test</h2>";

// Test 1: Check if config file is being loaded
echo "<h3>Test 1: Config File Loading</h3>";
try {
    require_once '../includes/config.php';
    echo "✅ Config file loaded successfully<br>";
    echo "DB_HOST: " . DB_HOST . "<br>";
    echo "DB_USERNAME: " . DB_USERNAME . "<br>";
    echo "DB_NAME: " . DB_NAME . "<br>";
    echo "DB_PASSWORD: " . (DB_PASSWORD ? "***SET***" : "NOT SET") . "<br>";
} catch (Exception $e) {
    echo "❌ Error loading config: " . $e->getMessage() . "<br>";
}

// Test 2: Check PDO connection
echo "<h3>Test 2: PDO Connection</h3>";
try {
    if (isset($pdo)) {
        echo "✅ PDO connection exists<br>";
        
        // Test a simple query
        $stmt = $pdo->query("SELECT 1 as test");
        $result = $stmt->fetch();
        echo "✅ Database query successful: " . $result['test'] . "<br>";
        
        // Check current database
        $stmt = $pdo->query("SELECT DATABASE() as current_db");
        $result = $stmt->fetch();
        echo "Current database: " . $result['current_db'] . "<br>";
        
        // Check user
        $stmt = $pdo->query("SELECT USER() as current_user");
        $result = $stmt->fetch();
        echo "Current user: " . $result['current_user'] . "<br>";
        
    } else {
        echo "❌ PDO connection not found<br>";
    }
} catch (Exception $e) {
    echo "❌ Database error: " . $e->getMessage() . "<br>";
}

// Test 3: Check session
echo "<h3>Test 3: Session Check</h3>";
if (session_status() === PHP_SESSION_ACTIVE) {
    echo "✅ Session is active<br>";
    echo "Session ID: " . session_id() . "<br>";
    
    if (isset($_SESSION['user_id'])) {
        echo "User ID: " . $_SESSION['user_id'] . "<br>";
    } else {
        echo "❌ No user_id in session<br>";
    }
    
    if (isset($_SESSION['user'])) {
        echo "User data exists in session<br>";
    } else {
        echo "❌ No user data in session<br>";
    }
} else {
    echo "❌ Session not active<br>";
}

// Test 4: Check functions
echo "<h3>Test 4: Function Check</h3>";
if (function_exists('isLoggedIn')) {
    echo "✅ isLoggedIn function exists<br>";
    echo "isLoggedIn result: " . (isLoggedIn() ? "true" : "false") . "<br>";
} else {
    echo "❌ isLoggedIn function not found<br>";
}

if (function_exists('isAdmin')) {
    echo "✅ isAdmin function exists<br>";
    echo "isAdmin result: " . (isAdmin() ? "true" : "false") . "<br>";
} else {
    echo "❌ isAdmin function not found<br>";
}

// Test 5: Try direct database connection
echo "<h3>Test 5: Direct Database Connection Test</h3>";
try {
    $test_pdo = new PDO("mysql:host=localhost;dbname=jozihum2y7r5_jozi_hustle_db", "jozihum2y7r5_jozi_hustle_db_user", "_3#A,F1M&ny$~UAF");
    $test_pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "✅ Direct connection successful<br>";
    
    $stmt = $test_pdo->query("SELECT COUNT(*) as user_count FROM users");
    $result = $stmt->fetch();
    echo "User count: " . $result['user_count'] . "<br>";
    
} catch (Exception $e) {
    echo "❌ Direct connection failed: " . $e->getMessage() . "<br>";
}

echo "<br><a href='index.php'>Back to Admin Dashboard</a>";
?>
