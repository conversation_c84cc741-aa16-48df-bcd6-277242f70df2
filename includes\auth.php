<?php
// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

/**
 * Check if user is logged in
 * Redirects to login page if not authenticated
 */
function requireLogin() {
    if (!isset($_SESSION['user'])) {
        header('Location: /JoziHustle/auth/login.php');
        exit;
    }
}

/**
 * Check if user is logged in without redirecting
 * @return boolean True if user is logged in, false otherwise
 */
function isLoggedIn() {
    return isset($_SESSION['user']);
}

/**
 * Get current logged in user data
 * @return array|null User data array or null if not logged in
 */
function getCurrentUser() {
    return $_SESSION['user'] ?? null;
}
